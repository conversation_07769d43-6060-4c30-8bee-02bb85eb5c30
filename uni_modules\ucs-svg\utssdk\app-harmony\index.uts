import { BuilderNode } from "@kit.ArkUI"
import { buildSVG } from "./builder.ets"

interface UcsSvgOptions {
    src : string,
	width: number,
	height: number
}

export class UcsSvg {

	$element : UniNativeViewElement
	private builder : BuilderNode<[UcsSvgOptions]> | null = null
	private params : UcsSvgOptions = {
	    src: '',
		width: 0,
		height: 0
	}

	constructor(element : UniNativeViewElement) {
		this.builder = element.bindHarmonyWrappedBuilder(wrapBuilder<[UcsSvgOptions]>(buildSVG), this.params)
		this.$element = element
		this.$element.bindHarmonyController(this)
	}

	setSource(src : string) {
		
		const regex = /viewBox="([^"]*)"/;
		const match = src.match(regex)?.[1]?.split(' ');
		
		this.params.src = src;
		this.params.width = Number(match?.[2]) || 0;
		this.params.height = Number(match?.[3]) || 0;
		
		this.builder?.update(this.params);
	}
}