<p align="center"><img alt="logo" src="https://ucs.cloudsimpler.com/logo/svg.svg" width="123"></p>
<h3 align="center">ucs-svg 矢量图形</h3>

## 简述
矢量图形组件是框架中用于渲染和操作 SVG 资源的组件。允许开发者将 SVG 图形封装成独立的组件，并在应用中灵活使用。SVG 图形组件具有响应式设计、组件化、易于维护、跨平台支持、丰富的社区和生态系统等特点。通过使用 SVG 图组件，开发者可以轻松地创建各种复杂和动态的 SVG 图形效果，以满足不同前端开发需求。

- 响应式设计：组件是响应式的，这意味着 SVG 图形可以随着数据的变化而自动更新。这使得 SVG 图形可以轻松地与 uni-app 其它部分同步变化。
- 组件化：组件化设计使得 SVG 图形可以作为独立的组件使用，便于代码的组织和复用。你可以将 SVG 图形封装成一个组件，然后在不同的组件中多次使用。
- 易于维护：具有良好的封装性，这使得 SVG 图形组件的维护更加方便。你可以独立地修改组件的代码，而不会影响到其他组件。
- 跨平台支持：支持多平台跨端，这意味着你可以使用相同的 SVG 图形组件在不同平台上运行，不需对修改代码完美适配。
- 社区和生态系统：有一个庞大的社区和丰富的生态系统，你可以找到大量的 SVG 图形组件和库。这些组件和库可以帮助你快速实现各种复杂的 SVG 图形效果。

## * 重要提示
因跨平台需求及性能考虑，`uni-app-x[app]` 和 `uni-app[app-nvue]` 采用 `uts` 插件实现以优化性能，这两端需要配置 `uts` 插件运行配置后 `自定义基座` 使用。其余端不受此约束，可直接使用。  
- [Android UTS插件运行编译配置](https://uniapp.dcloud.net.cn/tutorial/run/uts-development-android.html)
- [IOS UTS插件运行编译配置](https://uniapp.dcloud.net.cn/tutorial/run/uts-development-ios.html)

## 官方文档
官网地址：[https://ucs.cloudsimpler.com/library/ucs-svg](https://ucs.cloudsimpler.com/library/ucs-svg)

## 源码
[![stars](https://img.shields.io/github/stars/cloudsimpler/uni-ucs-design?style=social)](https://github.com/cloudsimpler/uni-ucs-design/tree/master/uni_modules/ucs-svg)
[![forks](https://img.shields.io/github/forks/cloudsimpler/uni-ucs-design?style=social)](https://github.com/cloudsimpler/uni-ucs-design/tree/master/uni_modules/ucs-svg)
[![watchers](https://img.shields.io/github/watchers/cloudsimpler/uni-ucs-design?style=social)](https://github.com/cloudsimpler/uni-ucs-design/tree/master/uni_modules/ucs-svg)
[![license](https://img.shields.io/github/license/cloudsimpler/uni-ucs-design?style=social)](https://github.com/cloudsimpler/uni-ucs-design/tree/master/uni_modules/ucs-svg)
[![star](https://gitee.com/cloudsimpler/uni-ucs-design/badge/star.svg?theme=white)](https://gitee.com/cloudsimpler/uni-ucs-design/tree/master/uni_modules/ucs-svg)
[![fork](https://gitee.com/cloudsimpler/uni-ucs-design/badge/fork.svg?theme=white)](https://gitee.com/cloudsimpler/uni-ucs-design/tree/master/uni_modules/ucs-svg)

## 版权信息
- 遵循 `MIT` 开源协议，无需支付任何费用，也无需授权，即可将框架应用到产品中。
- 仅供学习交流，如作它用所承受的法律责任一概与作者无关。
- 此插件采用[《uts svg组件》](https://ext.dcloud.net.cn/plugin?id=14948)部分代码，已获得插件作者[「码农朱哲」](https://ext.dcloud.net.cn/publisher?id=9053)的授权开源使用。

## 致谢
首先感谢 [DCloud](https://www.dcloud.io/) 官方，旗下出品的 [uni-app](https://uniapp.dcloud.net.cn/) 、[uni-app-x](https://uniapp.dcloud.net.cn/uni-app-x/) 、[uniCloud](https://uniapp.dcloud.net.cn/uniCloud/)、[uni-app 小程序](https://nativesupport.dcloud.net.cn/README) 等多平台、多元化的技术体系。  
其次感谢 [DCloud 插件市场](https://ext.dcloud.net.cn/) 开源作品的作者们，"捧着一颗心来，不带半棵草去。" 的开源奉献精神致敬。
