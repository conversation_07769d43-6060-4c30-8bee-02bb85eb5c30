<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标提取工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #005a87;
        }
        textarea {
            width: 100%;
            height: 300px;
            font-family: monospace;
            font-size: 12px;
        }
        .icon-preview {
            display: inline-block;
            margin: 5px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            width: 100px;
        }
        .icon-preview svg {
            width: 24px;
            height: 24px;
        }
        .icon-name {
            font-size: 10px;
            margin-top: 5px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>图标提取工具</h1>
    <p>这个工具可以帮助你从iconfont.js文件中提取所有图标数据</p>
    
    <div class="container">
        <h3>步骤1: 加载iconfont.js</h3>
        <button onclick="loadIconfont()">加载iconfont.js</button>
        <p id="loadStatus">等待加载...</p>
    </div>
    
    <div class="container">
        <h3>步骤2: 提取图标</h3>
        <button onclick="extractIcons()">提取所有图标</button>
        <button onclick="generateCode()">生成代码</button>
        <p>找到的图标数量: <span id="iconCount">0</span></p>
    </div>
    
    <div class="container">
        <h3>图标预览</h3>
        <div id="iconPreview"></div>
    </div>
    
    <div class="container">
        <h3>生成的代码</h3>
        <textarea id="generatedCode" placeholder="生成的图标映射代码将显示在这里..."></textarea>
        <br>
        <button onclick="copyCode()">复制代码</button>
    </div>

    <script src="../static/iconfont.js"></script>
    <script>
        let extractedIcons = {};
        
        function loadIconfont() {
            const status = document.getElementById('loadStatus');
            if (window._iconfont_svg_string_1313822) {
                status.textContent = '✅ iconfont.js 已成功加载';
                status.style.color = 'green';
            } else {
                status.textContent = '❌ iconfont.js 加载失败';
                status.style.color = 'red';
            }
        }
        
        function extractIcons() {
            const iconfontString = window._iconfont_svg_string_1313822;
            if (!iconfontString) {
                alert('请先加载iconfont.js文件');
                return;
            }
            
            extractedIcons = {};
            const symbolRegex = /<symbol\s+id="([^"]*)"[^>]*>(.*?)<\/symbol>/gs;
            let match;
            
            while ((match = symbolRegex.exec(iconfontString)) !== null) {
                const iconName = match[1];
                const iconContent = match[2];
                
                // 提取viewBox属性
                const viewBoxMatch = match[0].match(/viewBox="([^"]*)"/);
                const viewBox = viewBoxMatch ? viewBoxMatch[1] : '0 0 1024 1024';
                
                // 构建完整的SVG
                const svgContent = `<svg viewBox="${viewBox}" xmlns="http://www.w3.org/2000/svg">${iconContent}</svg>`;
                extractedIcons[iconName] = svgContent;
            }
            
            document.getElementById('iconCount').textContent = Object.keys(extractedIcons).length;
            showIconPreview();
        }
        
        function showIconPreview() {
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';
            
            for (const [iconName, svgContent] of Object.entries(extractedIcons)) {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-preview';
                iconDiv.innerHTML = `
                    ${svgContent}
                    <div class="icon-name">${iconName}</div>
                `;
                preview.appendChild(iconDiv);
            }
        }
        
        function generateCode() {
            if (Object.keys(extractedIcons).length === 0) {
                alert('请先提取图标');
                return;
            }
            
            let code = 'const iconDataMap = {\n';
            for (const [iconName, svgContent] of Object.entries(extractedIcons)) {
                // 转义单引号
                const escapedSvg = svgContent.replace(/'/g, "\\'");
                code += `\t'${iconName}': '${escapedSvg}',\n`;
            }
            code += '};';
            
            document.getElementById('generatedCode').value = code;
        }
        
        function copyCode() {
            const textarea = document.getElementById('generatedCode');
            textarea.select();
            document.execCommand('copy');
            alert('代码已复制到剪贴板');
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            loadIconfont();
        };
    </script>
</body>
</html>
