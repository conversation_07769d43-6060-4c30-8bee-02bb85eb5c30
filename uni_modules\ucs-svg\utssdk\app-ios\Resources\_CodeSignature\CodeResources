<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleUcsSvgX-Swift.h</key>
		<data>
		4w9Q266I4D4c3hAW51vlWyxjk90=
		</data>
		<key>Headers/unimoduleUcsSvgX.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		6efyXNVc+uUv2V1Wjthxm7tUlws=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		GtF0dG/1DFjcUcskXIBmXyIWZmI=
		</data>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		eQ6PGBLnIK7LJ8pWLwJ5Y8vbZm0=
		</data>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		RftCmjCeRs9tqxRD2Jt2WCC2M5o=
		</data>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		WWWzXQr69WCBPh2gxiMlkJORPlg=
		</data>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		eAYHCJc4IVDz9XvcnpGXA64fjXE=
		</data>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		WWWzXQr69WCBPh2gxiMlkJORPlg=
		</data>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		b+AMNxawoidfWRiMqlTxUxBX0Xk=
		</data>
		<key>config.json</key>
		<data>
		jo/qJhWkc5CQPkmEfWKkUS9c85Q=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleUcsSvgX-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			S6vYqkZeXSP/QQnRGSBQ5cgWLM/nil+MriKMA0LCwko=
			</data>
		</dict>
		<key>Headers/unimoduleUcsSvgX.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			UkQ1tz2JVFrEXTDdU8uvDhFwe+DYeB6BVYjR0ILDSqU=
			</data>
		</dict>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			7gTK6jmzEVW3lBeVXKboCRETnmUIlUK04AJ3tDvAi7c=
			</data>
		</dict>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JRexOk5P6T91Lo3oV7ZT9/5RGBYF5oOAcHmxEIv9IbQ=
			</data>
		</dict>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			pJ/Df0uQTaYYTOAW6bUg/Gg9zPGHJkKVNqWInNejWyc=
			</data>
		</dict>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			QNleRhnykh6RHoHJdRxEh1Cw+yLHQl+qH8Y5MhwQ1w0=
			</data>
		</dict>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			pJ/Df0uQTaYYTOAW6bUg/Gg9zPGHJkKVNqWInNejWyc=
			</data>
		</dict>
		<key>Modules/unimoduleUcsSvgX.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			3HhunfRwpxIIxXruuX9ar5B1+n7F/sLYWU11Jt3Th0o=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Fo/X5AmNDDUqJ4o8kFF60K9pTDiQEafR9Rzvsmrg4Mk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
