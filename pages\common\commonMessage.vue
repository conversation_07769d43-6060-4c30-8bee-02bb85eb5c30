<template>
	<view class="static">
		<uni-nav-bar status-bar :fixed="true" :border="false">
			<block v-slot:left>
				<uni-icons @click="navigateBack()" type="left" size="30"></uni-icons>
			</block>
			<view class="flex fontSize-34  w-full  justify-center items-center">
				<text v-text="title"></text>
			</view>
			<block v-if="rightText" v-slot:right>
				<view @click="navigateToSecondPage()" class="flex fontSize-28  w-full justify-center items-center">
					<text v-text="rightText"></text>
				</view>

				<!-- @click="navigateToUrl(`${rightUrl}`)"	<uni-icons @click="toggleMenu()" type="more-filled" size="30"></uni-icons> -->
			</block>
		</uni-nav-bar>
		
		<view class="flex absolute" style="width: 100%;"
			:style="{height: `calc(100vh - ${navBarHeight}px)`,top:`${navBarHeight}px`}">
			<web-view v-if="webViewUrl && webViewUrl.startsWith('http')" @message="handleWebViewMessage" :webview-styles="webviewStyles" :fullscreen="false"
				style="height:100%;width:100%" :src='webViewUrl'></web-view>
				
			<view v-else class="flex h-full w-full justify-center items-center bg-gray-100">
				
				<view class="flex flex-col  items-center">
					<image src="../../static/images/404.png" mode="widthFix" style="width: 160px; margin-bottom: 20px; opacity: 0.7;"  />
					<text style="font-size: 32px; color: #888; font-weight: bold;"></text>
					<text style="font-size: 16px; color: #aaa; margin-top: 8px;">页面未找到</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	// React 前端项目可以继续使用原有的 Inherit.js 基类，无需修改任何代码，实现了最大程度的兼容性。
	import {
		initNFC,
		getNfcText,
		closeNFC,
		setNfcType,
		setNfcText
	} from '@/utils/nfcSoft.js';
	import {
		ref,
		computed,
		onMounted,
		getCurrentInstance
	} from 'vue';
	const NFCTimer = ref(null)
	const title = ref("")
	const rightText = ref("") //使用 rightText 属性设置导航栏右侧文字
	const rightUrl = ref("") //使用 rightUrl 属性设置导航栏右侧地址
	const defaultPageTitle = ref("")
	const navBarHeight = ref(44) // 顶部标题高度https://www.baidu.com
	const tabBarHeight = ref(50) // 底部菜单栏高度 
	const otherHeight = ref(0)
	//const webViewUrl = ref('http://**************:19719/subapp/customform/designview?formName=265809164288069&retrunPath=designlist') //https://www.baidu.com http://localhost:5173/#/FirstMaterialDetail
	const webViewUrl = ref('')// ref('http://**************:19719/subapp/PCBSFC/?/App/WorkSubmit')
	const reloadWebViewUrl = ref('')
	const systemInfo = uni.getSystemInfoSync(); //系统信息

	const windowHeight = computed(() => {
		return systemInfo.windowHeight
	})
	const webviewStyles = ref({
		top: (navBarHeight.value + systemInfo.safeArea.top) + 'px', //  非自定义页面，不需要顶部菜单栏+安全栏 位置
		width: '100%',
		height: (windowHeight.value - navBarHeight.value - systemInfo.safeArea.top) + 'px',
		progress: {
			color: '#FF3333'
		}
	})
	onMounted(() => {

		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();
		// 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
		eventChannel.on('acceptDataFromOpenerPage', function(data) {
			console.log('消息：acceptDataFromOpenerPage', JSON.stringify(data))
			if (data.CTITLE) {
				// 设置当前页面标题
				title.value = data.CTITLE
				// debugger
				defaultPageTitle.value = data.CTITLE
				uni.setNavigationBarTitle({
					title: data.CTITLE,
					success: function() {
						console.log('Navigation bar title set successfully.');
					},
					fail: function(err) {
						console.error('Failed to set navigation bar title.', err);
					}
				});
			}
			if(data.CBEHAVIOR_PATH && data.CBEHAVIOR_PATH.startsWith('http')){
				webViewUrl.value = data.CBEHAVIOR_PATH
			}
		})

	})


	function handleWebViewMessage(event) {
	
		console.log('======handleWebViewMessage from h5=========', JSON.stringify(event))
		//let params = event.detail.data[0]
		let params;
		// 处理不同格式的消息数据
		if (event.detail && event.detail.data) {
			params = event.detail.data[0];
		} else if (event.detail) {
			// 直接从 detail 获取数据
			try {
				params = typeof event.detail === 'string' ? JSON.parse(event.detail) : event.detail;
			} catch (e) {
				console.error('Failed to parse message data:', e);
				return;
			}
		} else {
			console.error('Invalid message format:', event);
			return;
		}
		let actionType = params.type
		switch (actionType) {

			// 隐藏已经显示的软键盘，如果软键盘没有显示则不做任何操作
			case "hideKeyboard":
				uni.hideKeyboard()
				break;
				//扫描二维码 设备
			case "scanbarcode":
				// {  
				//   "type": "scanbarcode",
				//    key: indexData
				//  } 
				scanbarcode(params)
				break;

				//显示导航栏二级页面通过壳打开	
			case "showrighttext":
				// {
				//           type: "showrighttext",
				//           state: 0,
				//           right_url: "",
				//           callfunc: callback,
				//           right_text: text,
				//       }
				showrighttext(params)
				break;
			case "goToWeb2":
				// {
				//           type: "goToWeb2",
				//           title: title,
				//           url: url,
				//       }
				goToWeb2(params)
				break;
				/**
				 * 跳转到三级页面（如：详细记录）
				 * @param {*} title 页面标题
				 * @param {*} url 页面路径
				 */
			case "goToWeb3":
				// {
				//             type: "goToWeb3",
				//             title: title,
				//             url: url,
				//         }
				goToWeb3(params)
				break;
				/**
				 * 向本地写入数据（数据持久化）
				 * @param {string} key 数据存储的key值
				 * @param {string} value 数据
				 */
			case "writedata":
				// {
				//           type: "writedata",
				//           key: key,
				//           value: value,
				//           valuetype: "1",
				//       }
				writedata(params)
				break;
			case "readdata":
				//读取APP壳的HttpHeader信息
				// {
				//     "type": "readdata",
				//     "key": "HttpHeader",
				//     "types": 1,
				//     "backfunc": "Readinfor"
				// };
				readdata(params)
				break;
				//设置横屏
			case "lockToLandscape":
				// {
				//             type: 'lockToLandscape'
				//         }
				lockToLandscape(params)
				break;
				/**
				 * PDA页面加载自动聚焦到第一个
				 * 输入框，在调用this.xxInput.focus()
				 * 前需要先调用这个方法
				 * callback 回调方法
				 */
				// case "requestFocus":
				// 	// {
				// 	//             type: "requestFocus",
				// 	//         }
				// 	requestFocus(params)
				// 	break;
				/**
				 * 返回上一页
				 */
			case "goBack":
				// {
				//             type: "goBack"
				//         }
				navigateBack(params)
				break;
				/**
				 * 刷新前一个页面
				 */
			case "refreshFront":
				// {
				//            type: "refreshFront"
				//        }
				refreshFront(params)
				break;
				/**
				 * 推送消息（走APP推送）
				 * @param {*} message 消息体
				 */
			case "PushMessage":
				// {
				//             type: "PushMessage",
				//             message
				//         }
				PushMessage(params)
				break;
				/**
				 * 推送企业微信消息
				 * @param {*} touser 推送用户 
				 * @param {*} message 消息内容
				 */
			case "text":
				//{
				//     touser: touser,
				//     msgtype: "text",
				//     content: message
				// }
				PushText(params)
				break;
			case "NFC":
				// {  
				//  "type": "NFC",
				//   key: indexData // 其它参数，可能多个NFC功能
				//   } 
				NFC_fn(params)
				break;
			default:
				break;
		}
	}
	// 跳转到三级页面（如：详细记录）
	function goToWeb3(fromParams) {
		let params = {
			title: fromParams.title,
			url: fromParams.url
		}
		uni.navigateTo({
			url: '/pages/common/thirdPage',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromsecondPage', params)
			}
		})
	}
	// 设置横屏
	function lockToLandscape() {
		//plus.screen.unlockOrientation()
		console.log("====lockToLandscape start====")
		plus.screen.lockOrientation('landscape - primary');
		console.log("====lockToLandscape end====")
	}
	// 启用NFC功能
	function NFC_fn(params) {
		//let _self = this
		// 无论是否能读取，10秒后停止读取
		setTimeout(function() {
			closeNFC();
			clearInterval(NFCTimer.value)
			uni.hideLoading();
		}, 10000);
		setNfcType('read') // 设置读取类型 ['cardNo', 'write', 'read']
		// 初始化 NFC，并且立即开启读取NFC功能
		initNFC()
		uni.showLoading({
			title: 'NFC 读取中...'
		});
		setIntervalNFC(params)
	}
	// 每秒循环读取NFC内容一次 ，如果读取到，立刻停止读取循环
	function setIntervalNFC(event) {
		//let _self = this

		NFCTimer.value = setInterval(() => {

			let readNFCText = getNfcText()
			if (readNFCText) {
				// uni.showToast({
				// 	title: 'sending message...',
				// 	icon: 'none'
				// })
				let params = {
					data: readNFCText,
					params: event
				}
				responseDataToWebView(params)
				closeNFC();
				clearInterval(NFCTimer.value)
				uni.hideLoading();
			}
		}, 1000)
	}
	// 推送企业微信消息
	function PushText() {

	}
	// 推送消息（走APP推送）
	function PushMessage(params) {
		// 设置角标
		setTimeout(() => {
			uni.showTabBarRedDot({
				index: 3,
				//  text: '10' // 不支持
			})
		}, 1000)
		// uni.hideTabBarRedDot(
		// 		{
		// 				  index: 3,
		// 				//  text: '10' // 不支持
		// 			}
		// )
	}

	function writedata(params) {
		// {
		//           type: "writedata",
		//           key: key,
		//           value: value,
		//           valuetype: "1",
		//       }
		try {
			uni.setStorageSync(params.key, params.value);
		} catch (e) {
			console.error('写入APP壳的writedata信息 错误，key:', params.key)
		}
	}

	//读取APP壳的HttpHeader信息
	function readdata(params) {
		// {
		//     "type": "readdata",
		//     "key": "HttpHeader",
		//     "types": 1,
		//     "backfunc": "Readinfor"
		// }
		try {
			const value = uni.getStorageSync(params.key);
			if (value) {
				console.log('读取APP壳的readdata信息:', value);
				// let params = {
				// 				data: value,
				// 				params: 'Readinfor'
				// 			}
				// responseDataToWebView(params)
				let responseParams = {
					data: value,
					params: params.backfunc || 'Readinfor'
				}
				responseDataToWebView(responseParams)
			}
		} catch (e) {
			console.error('读取APP壳的readdata信息 错误，key:', params.key)
		}
	}
	// 刷新前一个页面
	function refreshFront(params) {
		//重新加载 web-view 组件当前页面
		reloadWebViewUrl.value = webViewUrl.value
		webViewUrl.value = ""
		setTimeout(() => {
			webViewUrl.value = reloadWebViewUrl.value
		}, 1000)
	}

	// 执行WEBVIEW回调函数 - 增强版本
	function responseDataToWebView(data, fnName = '') {
		console.log("responseDataToWebView：", JSON.stringify(data))

		// 原有的 uni-app 方式
		const _pages = getCurrentPages();
		const _currentPage = _pages[_pages.length - 1];
		const _funName = !!fnName ? fnName : 'messageFromUniApp'
		const _data = {
			data: data
		};
		const currentWebview = _currentPage.$getAppWebview().children()[0];
		currentWebview.evalJS(`${_funName}(${JSON.stringify(_data)})`);

	}


	function toggleMenu(type) {
		uni.showActionSheet({
			itemList: ['全部已读', '全部删除'],
			success: function(res) {
				console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
			},
			fail: function(res) {
				console.log(res.errMsg);
			}
		});
	}
	/////////////////////调用APP壳的功能方法/////////////////////
	function showrighttext(params) {
		console.log("=set showrighttext=======", JSON.stringify(params))
		//           right_url: "",
		//           callfunc: callback,
		//           right_text: text,
		rightText.value = params.right_text
		rightUrl.value = params.right_url
	}
	//开启二维码读取功能
	function scanbarcode(fromParams) {
		uni.scanCode({
			success: (res) => {
				let params = {
					data: res.result,
					params: fromParams
				}
				responseDataToWebView(params)
			},
			fail: (err) => {
				console.log('扫描失败', err)
			}
		})
	}
	// 返回上一个页面
	function navigateBack() {
		uni.navigateBack()
	}
	// 设置标题
	function setTitle(title) {
		title.value = title
		if (!title) {
			// 如果为空，保留首次APP进入的标题
			title.value = defaultPageTitle.value
		}
		// uni.setNavigationBarTitle({
		// 	title: title.value,
		// 	success: function() {
		// 		console.log('Navigation bar title set successfully.');
		// 	},
		// 	fail: function(err) {
		// 		console.error('Failed to set navigation bar title.', err);
		// 	}
		// });
	}

	function goToWeb2(fromParams) {
		let params = {
			title: fromParams.title,
			url: fromParams.url
		}
		uni.navigateTo({
			url: '/pages/common/secondPage',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromCommonPage', params)
			}
		})
	}
	// 顶部-右侧按钮调整功能
	function navigateToSecondPage() {
		// 跳转到二级自定义WEBVIEW页面，如历史记录
		let params = {
			title: rightText.value,
			url: rightUrl.value
		}
		uni.navigateTo({
			url: '/pages/common/secondPage',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromCommonPage', params)
			}
		})
	}

	function navigateTo(fromParams) {
		//在起始页面跳转到test.vue页面并传递参数
		uni.navigateTo({
			url: fromParams.url, //'test?id=1&name=uniapp'
			success: function(res) {
				setTitle(fromParams.title)
			},
			fail: function(res) {
				setTitle("")
			}
		});
	}
</script>

<style>

</style>