/**
 * 图标提取工具
 * 用于从iconfont.js中提取SVG图标数据
 */

/**
 * 从iconfont字符串中提取所有图标
 * @param {string} iconfontString - iconfont.js中的SVG字符串
 * @returns {Object} 图标映射对象
 */
export function extractAllIcons(iconfontString) {
	const iconMap = {};
	
	// 匹配所有symbol标签
	const symbolRegex = /<symbol\s+id="([^"]*)"[^>]*>(.*?)<\/symbol>/gs;
	let match;
	
	while ((match = symbolRegex.exec(iconfontString)) !== null) {
		const iconName = match[1];
		const iconContent = match[2];
		
		// 提取viewBox属性
		const viewBoxMatch = match[0].match(/viewBox="([^"]*)"/);
		const viewBox = viewBoxMatch ? viewBoxMatch[1] : '0 0 1024 1024';
		
		// 构建完整的SVG
		const svgContent = `<svg viewBox="${viewBox}" xmlns="http://www.w3.org/2000/svg">${iconContent}</svg>`;
		iconMap[iconName] = svgContent;
	}
	
	return iconMap;
}

/**
 * 从全局变量中获取iconfont字符串
 * @returns {string} iconfont字符串
 */
export function getIconfontString() {
	try {
		// 尝试多种方式获取全局的iconfont字符串
		if (typeof window !== 'undefined' && window._iconfont_svg_string_1313822) {
			return window._iconfont_svg_string_1313822;
		}
		if (typeof globalThis !== 'undefined' && globalThis._iconfont_svg_string_1313822) {
			return globalThis._iconfont_svg_string_1313822;
		}
		if (typeof global !== 'undefined' && global._iconfont_svg_string_1313822) {
			return global._iconfont_svg_string_1313822;
		}
		return '';
	} catch (error) {
		console.error('获取iconfont字符串失败:', error);
		return '';
	}
}

/**
 * 生成图标映射代码
 * 这个函数可以帮助你生成iconDataMap的代码
 */
export function generateIconMapCode() {
	const iconfontString = getIconfontString();
	if (!iconfontString) {
		console.warn('未找到iconfont字符串');
		return;
	}
	
	const iconMap = extractAllIcons(iconfontString);
	const iconNames = Object.keys(iconMap);
	
	console.log('找到的图标数量:', iconNames.length);
	console.log('图标名称列表:', iconNames);
	
	// 生成代码
	let code = 'const iconDataMap = {\n';
	for (const [iconName, svgContent] of Object.entries(iconMap)) {
		code += `\t'${iconName}': '${svgContent}',\n`;
	}
	code += '};';
	
	console.log('生成的图标映射代码:');
	console.log(code);
	
	return iconMap;
}

/**
 * 获取单个图标的SVG数据
 * @param {string} iconName - 图标名称
 * @returns {string} SVG字符串
 */
export function getSingleIcon(iconName) {
	const iconfontString = getIconfontString();
	if (!iconfontString) {
		return null;
	}
	
	const symbolRegex = new RegExp(`<symbol\\s+id="${iconName}"[^>]*>(.*?)</symbol>`, 's');
	const match = iconfontString.match(symbolRegex);
	
	if (match) {
		const viewBoxMatch = match[0].match(/viewBox="([^"]*)"/);
		const viewBox = viewBoxMatch ? viewBoxMatch[1] : '0 0 1024 1024';
		return `<svg viewBox="${viewBox}" xmlns="http://www.w3.org/2000/svg">${match[1]}</svg>`;
	}
	
	return null;
}
