<template>

	<view>
		<view :style="{ height: `${windowBodyHeight}px` }" class="flex bg-white w-full">
			<view class="flex justify-between  w-full flex-col">
				<!-- 分类  -->
				<u-sticky style="background-color: #efeff4;" class="p-all-20  " offsetTop="0">
					<uni-easyinput prefixIcon="search" @change="searchAppClick" @clear="searchAppClick"
						v-model="searchInput" placeholder="搜索插件" @iconClick="searchAppClick"></uni-easyinput>
				</u-sticky>
				<!-- 列表内容 -->
				<scroll-view scroll-y="true" @scroll="onScroll" class="flex-1 overflow-y-auto">
					<view class="  flex  flex-col pl-20 pr-20 ">

						<block v-if="messageList">
											<uni-list class="mb-80">
												<uni-list ellipsis="1" :border="true">
													<uni-list-chat :avatar-circle="true"  v-for="(item,index) in pluginList" :key="index"  :title="item.CFILE_NAME" avatar="/static/images/appICON.png" :note="`${item.CMD5}`" :time="formatTime(item.CDATETIME_FILE_MODIFIED,'YY-MM-DD')">
														
													</uni-list-chat>
													
													
													
												</uni-list>
											</uni-list>
										</block>
										<block v-else>
											<view class="flex justify-center items-center h-full ">
												暂无数据
											</view>
											
						</block>


					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import dayjs from 'dayjs'
import * as serviceApi from '@/api/index.js'
import { onPullDownRefresh } from '@dcloudio/uni-app';
import { getCurrentUserInfoByField } from '@/utils/tools.js'
import {
	ref,
	onMounted,
	computed
} from 'vue'
import {
	setStorageSync,
	getStorageSync,
	CURRENT_SERVER
} from '@/utils/Storage.js'
const pluginList = ref(
	  [
                // {
                //     "CID": "0",
                //     "CBUCKET_NAME": "appicon",
                //     "CIMAGE_NAME": "menu_控制台.png",
                //     "CIMAGE_PATH": "http://************:9001/flowchart/0.png",
                //     "CIMAGE_TYPE": "image/png",
                //     "CIMAGE_BODY": "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",
                //     "CMD5": "4086ba63fa8d0106adeebb3e03c21e1b"
                // }
               
        ]
	)
const isOpenAllCollapse = ref(true) // 是否所有折叠
const messageList = ref(99)
const navBarHeight = ref(44) // 顶部标题高度
const tabBarHeight = ref(50) // 底部菜单栏高度	
const isEdit = ref(false)
const currentLongPressTab = ref(null)
const menuCategory = ref([])
const searchInput = ref('')
const copyPluginList = ref([])
const systemInfo = uni.getSystemInfoSync(); //系统信息
const windowHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight
})
const windowBodyHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight - navBarHeight.value - systemInfo.safeArea.top
})

onMounted(() => {
	 checkDataBeforeLoad()
})

// 下拉刷新处理函数
onPullDownRefresh(async () => {
	console.log('下拉刷新处理函数');
	try {
		// 重新加载数据
		getPluginList()

		// 停止刷新动画（必须调用）
		setTimeout(function () {
			uni.stopPullDownRefresh({
			
			});
		}, 1000);
	} catch (error) {
		console.error('刷新失败:', error);
		setTimeout(function () {
			// 失败时也要停止动画
			uni.stopPullDownRefresh();
		}, 1000);
	}
});
//////////////////////methods//////////////////////////
/**
 * 格式化时间的函数
 * @param {Date|string|number} time - 要格式化的时间，可以是Date对象、时间字符串或时间戳
 * @param {string} format - 格式化模板，默认为'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(DATE){
	if(DATE){
		return	dayjs(DATE).format("YY/MM/DD HH:mm")
	}else{
		DATE
	}
	
}
// function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
//     let date;
//     // 处理不同类型的时间输入
//     if (time instanceof Date) {
//         date = time;
//     } else if (typeof time === 'string') {
//         date = new Date(time);
//         if (isNaN(date.getTime())) {
//             date = new Date(parseInt(time, 10));
//         }
//     } else if (typeof time === 'number') {
//         date = new Date(time);
//     } else {
//         date = new Date();
//     }

//     if (isNaN(date.getTime())) {
//         throw new Error('无效的时间格式');
//     }

//     // 提取时间各部分
//     const year = date.getFullYear();
//     const shortYear = year % 100; // 取年份后两位
//     const month = date.getMonth() + 1;
//     const day = date.getDate();
    
//     // 补零函数
//     const padZero = (num) => num.toString().padStart(2, '0');

//     // 替换格式中的占位符，增加对YY的支持
//     return format
//         .replace('YYYY', year)
//         .replace('YY', padZero(shortYear)) // 两位数年份，自动补零
//         .replace('MM', padZero(month))
//         .replace('DD', padZero(day))
//         .replace('HH', padZero(date.getHours()))
//         .replace('mm', padZero(date.getMinutes()))
//         .replace('ss', padZero(date.getSeconds()));
// }
function imageError(err){

}
function checkDataBeforeLoad() {
	let _Data = getStorageSync('PLUGIN_LIST')
	if (_Data) {
		pluginList.value = _Data
	} else {
		getPluginList()
	}
}

const isScrolling = ref(false)
const scrollTimer = ref(null)
function onScroll() {
	isScrolling.value = true;
	// 2. 清除之前的计时器（避免多次触发时计时混乱）
	if (scrollTimer.value) {
		clearTimeout(scrollTimer.value);
	}

	// 3. 启动新计时器：300ms 后若没有再滚动，视为滚动结束
	scrollTimer.value = setTimeout(() => {
		isScrolling.value = false; // 标记滚动结束
		scrollTimer.value = null; // 清空计时器
		console.log('isScrolling.value:', isScrolling.value)
	}, 500);
}

//  获取app图标并下载到本地
	function getPluginList() {
		let params =[]
		serviceApi.getPluginsVersion().then(res => {
			if (res && res.data.code === 200 && res.data.data) {
				// 保存自定义图标信息
				pluginList.value = res.data.data.Datas
				setStorageSync('PLUGIN_LIST', res.data.data.Datas)
			} else {

			}
		}).catch(err => {
			console.log('获取失败:', err)
		})
	}


function searchAppClick() {
  let val = searchInput.value.trim();
  if (!copyPluginList.value.length) {
	// 首次搜索时备份原始数据
	copyPluginList.value = pluginList.value.slice();
  }
  if (val) {
	// 过滤 pluginList
	pluginList.value = copyPluginList.value.filter(item => {
	  // 支持名称、MD5、类型等字段模糊搜索
	  return (
		(item.CFILE_NAME && item.CFILE_NAME.toLowerCase().includes(val.toLowerCase())) ||
		(item.CMD5 && item.CMD5.toLowerCase().includes(val.toLowerCase())) 
	  );
	});
	messageList.value = pluginList.value.length > 0 ? 99 : 0;
  } else {
	// 搜索框清空，恢复原始数据
	pluginList.value = copyPluginList.value.slice();
	messageList.value = pluginList.value.length > 0 ? 99 : 0;
  }
}
</script>


<style lang="scss">
.imageItem {
	width: 60rpx;
	height: 60rpx;
}

.textDesc {
	font-size: 24rpx;
	margin-top: 10rpx;
	color: #363636; //#8c8c8c;
}
</style>